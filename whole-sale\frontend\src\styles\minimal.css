/* Minimal Clean Design for Wholesale Management System */

:root {
  /* Main colors */
  --primary-color: #3a7bd5;
  --primary-light: #5a9be6;
  --primary-dark: #2c5f9e;
  --secondary-color: #f0f4f8;
  --accent-color: #00c2a8;

  /* Text colors */
  --text-dark: #2d3748;
  --text-medium: #4a5568;
  --text-light: #718096;

  /* Status colors */
  --success-color: #38a169;
  --success-light: #c6f6d5;
  --warning-color: #e9b949;
  --warning-light: #fefcbf;
  --danger-color: #e53e3e;
  --danger-light: #fed7d7;

  /* UI colors */
  --border-color: #e2e8f0;
  --background-color: #f8fafc;
  --card-background: #ffffff;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;

  /* Border radius */
  --radius-sm: 0.125rem;
  --radius: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;

  /* Transitions */
  --transition: all 0.2s ease-in-out;
}

/* Base styles */
body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  color: var(--text-dark);
  background-color: var(--background-color);
  margin: 0;
  padding: 0;
  line-height: 1.5;
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition);
}

a:hover {
  color: var(--primary-dark);
}

button {
  cursor: pointer;
}

/* Layout */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* Header */
.header {
  background-color: white;
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-md) 0;
  box-shadow: var(--shadow-sm);
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.brand {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-color);
}

.nav {
  display: flex;
  gap: var(--spacing-xl);
}

.nav-link {
  color: var(--text-medium);
  font-weight: 500;
  padding: var(--spacing-sm) 0;
  border-bottom: 2px solid transparent;
  transition: var(--transition);
}

.nav-link:hover {
  color: var(--primary-color);
}

.nav-link.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

/* Page header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: var(--spacing-xl) 0;
}

.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-dark);
  margin: 0;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: var(--radius);
  transition: var(--transition);
  border: none;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  color: white;
}

.btn-outline {
  background-color: transparent;
  border: 1px solid var(--border-color);
  color: var(--text-medium);
}

.btn-outline:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.btn-danger {
  background-color: var(--danger-color);
  color: white;
}

.btn-danger:hover {
  background-color: #c53030;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

/* Cards */
.card {
  background-color: var(--card-background);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow);
  margin-bottom: 1.5rem;
  overflow: hidden;
}

.card-body {
  padding: var(--spacing-lg);
}

/* Forms */
.form-group {
  margin-bottom: var(--spacing-md);
  display: flex;
  flex-direction: column;
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  color: var(--text-medium);
}

.form-control {
  width: 100%;
  height: 38px; /* Fixed height for all form controls */
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  transition: var(--transition);
  box-sizing: border-box; /* Ensures padding doesn't affect overall height */
  line-height: 1.5;
  display: block;
}

/* Ensure select elements have the same height and appearance */
select.form-control {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='5' viewBox='0 0 8 5'%3E%3Cpath fill='%234a5568' d='M0 0l4 5 4-5z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 8px 5px;
  padding-right: 2rem;
}

/* Special handling for textareas */
textarea.form-control {
  height: auto;
  min-height: 38px;
  resize: vertical;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-light);
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.15);
}

/* Tables */
.table-container {
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 0.75rem 1rem;
  text-align: left;
}

.table th {
  background-color: var(--secondary-color);
  font-weight: 600;
  color: var(--text-medium);
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.table tr {
  border-bottom: 1px solid var(--border-color);
}

.table tr:last-child {
  border-bottom: none;
}

.table tr:hover {
  background-color: rgba(0, 0, 0, 0.01);
}

/* Status badges */
.badge {
  display: inline-block;
  padding: 0.25em 0.75em;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: 9999px;
}

.badge-success {
  background-color: var(--success-light);
  color: var(--success-color);
}

.badge-warning {
  background-color: var(--warning-light);
  color: var(--warning-color);
}

.badge-danger {
  background-color: var(--danger-light);
  color: var(--danger-color);
}

/* Action links */
.action-link {
  color: var(--primary-color);
  font-weight: 500;
  margin-right: 0.75rem;
}

.action-link:hover {
  text-decoration: underline;
}

.action-link.edit {
  color: var(--primary-color);
}

.action-link.delete {
  color: var(--danger-color);
}

/* Utilities */
.d-flex {
  display: flex;
}

.justify-between {
  justify-content: space-between;
}

.align-center {
  align-items: center;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-4 {
  gap: 1rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-4 {
  margin-top: 1rem;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

/* Login and Register Pages */
.login-container {
  display: flex;
  min-height: 100vh;
}

.login-sidebar {
  flex: 1;
  background-color: var(--primary-color);
  color: white;
  padding: 3rem 2rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.login-sidebar h1 {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.login-sidebar p {
  margin-bottom: 2rem;
  opacity: 0.9;
}

.login-feature {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.login-feature-icon {
  width: 24px;
  height: 24px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
  font-size: 0.875rem;
}

.login-form {
  flex: 1;
  padding: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-card {
  width: 100%;
  max-width: 450px;
  padding: 2rem;
  background-color: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
}

.login-card h2 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.login-card p {
  color: var(--text-light);
  margin-bottom: 1.5rem;
}

.login-btn {
  width: 100%;
  padding: 0.75rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.login-btn:hover {
  background-color: var(--primary-dark);
}

.alert {
  padding: 0.75rem 1rem;
  border-radius: var(--radius);
  margin-bottom: 1rem;
  position: relative;
}

.alert-danger {
  background-color: var(--danger-light);
  color: var(--danger-color);
}

.invalid-feedback {
  color: var(--danger-color);
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.is-invalid {
  border-color: var(--danger-color) !important;
}

/* Footer Styles */
.footer {
  background-color: var(--text-dark);
  color: white;
  margin-top: 3rem;
}

.footer-top {
  padding: 3rem 0;
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.footer-column {
  display: flex;
  flex-direction: column;
}

.footer-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: white;
  position: relative;
}

.footer-title::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 0;
  width: 50px;
  height: 2px;
  background-color: var(--primary-color);
}

.footer-description {
  color: #cbd5e0;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.footer-social {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: white;
  transition: var(--transition);
  text-decoration: none;
}

.social-icon:hover {
  background-color: var(--primary-color);
  transform: translateY(-3px);
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 0.75rem;
}

.footer-link {
  color: #cbd5e0;
  text-decoration: none;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
}

.footer-link:hover {
  color: white;
  transform: translateX(5px);
}

.footer-contact p {
  margin-bottom: 1rem;
  display: flex;
  align-items: flex-start;
  color: #cbd5e0;
}

.footer-contact i {
  margin-right: 0.75rem;
  color: var(--primary-color);
}

.footer-bottom {
  background-color: rgba(0, 0, 0, 0.2);
  padding: 1.5rem 0;
}

.footer-bottom .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.copyright {
  color: #a0aec0;
  margin: 0;
}

.footer-bottom-links {
  display: flex;
  gap: 1.5rem;
}

.footer-bottom-link {
  color: #a0aec0;
  text-decoration: none;
  transition: var(--transition);
  font-size: 0.875rem;
}

.footer-bottom-link:hover {
  color: white;
}

/* Responsive */
@media (max-width: 768px) {
  .nav {
    gap: var(--spacing-md);
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }

  .login-container {
    flex-direction: column;
  }

  .login-sidebar {
    padding: 2rem 1.5rem;
  }

  .form-row {
    flex-direction: column;
  }

  .detail-row {
    flex-direction: column;
  }

  .footer-bottom .container {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}

/* Product View Styles */
.product-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.product-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.product-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.category-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.product-sku {
  color: var(--text-light);
  font-size: 0.875rem;
}

.product-details {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.detail-row {
  display: flex;
  gap: 2rem;
}

.detail-group {
  flex: 1;
}

.detail-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-light);
  margin-bottom: 0.5rem;
}

.detail-text {
  margin: 0;
  font-size: 1rem;
}

.detail-text.price {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary-color);
}

/* Form Styles */
.form-row {
  display: flex;
  gap: 2rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  align-items: flex-start; /* Align items at the top */
}

.form-row .form-group {
  flex: 1;
  min-width: 200px; /* Ensures minimum width before wrapping */
  display: flex;
  flex-direction: column;
}

.form-row.two-cols .form-group {
  flex-basis: calc(50% - 1rem);
  flex-grow: 0;
}

/* Ensure all labels in a row have the same height */
.form-row .form-label {
  margin-bottom: 0.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
}

.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 1.25rem;
  color: var(--primary-color);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: var(--text-medium);
}

.btn-secondary:hover {
  background-color: #e2e8f0;
  color: var(--text-dark);
}

.mr-2 {
  margin-right: 0.5rem;
}

.alert-success {
  background-color: var(--success-light);
  color: var(--success-color);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  background-color: white;
  border-radius: var(--radius);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 500px;
  overflow: hidden;
}

.modal-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

.text-danger {
  color: var(--danger-color);
}

.btn-danger {
  background-color: var(--danger-color);
  color: white;
}

.btn-danger:hover {
  background-color: #e53e3e;
}

.empty-state {
  padding: 2rem;
  text-align: center;
  color: var(--text-medium);
}

/* Performance Bar Styles */
.performance-bar {
  width: 100%;
  background-color: #e5e7eb;
  border-radius: 9999px;
  height: 8px;
  overflow: hidden;
  margin-bottom: 4px;
}

.performance-progress {
  height: 100%;
}

.performance-text {
  font-size: 12px;
  color: var(--text-light);
}

/* Dashboard Stat Card Styles */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: var(--shadow);
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease;
  cursor: pointer;
  text-decoration: none;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.stat-title {
  font-size: 0.875rem;
  opacity: 0.9;
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.stat-link {
  margin-top: auto;
  font-size: 0.875rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  opacity: 1;
}

.stat-card:hover .stat-link {
  opacity: 1;
}

.stat-card-blue {
  background: linear-gradient(135deg, #2c5282, #3182ce);
  color: white !important;
}

.stat-card-red {
  background: linear-gradient(135deg, #c53030, #e53e3e);
  color: white !important;
}

.stat-card-green {
  background: linear-gradient(135deg, #276749, #38a169);
  color: white !important;
}

.stat-card-purple {
  background: linear-gradient(135deg, #6b46c1, #805ad5);
  color: white !important;
}

/* Ensure all text elements inside stat cards remain white */
.stat-card-blue *, .stat-card-red *, .stat-card-green *, .stat-card-purple * {
  color: white !important;
}

/* Dashboard Grid and Quick Actions */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.card-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.quick-action-btn {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: var(--secondary-color);
  color: var(--text-medium);
  border-radius: var(--radius);
  text-decoration: none;
  transition: transform 0.2s ease;
}

.quick-action-btn:hover {
  transform: translateY(-2px);
}

.quick-action-icon {
  margin-right: 10px;
  font-size: 1.25rem;
}

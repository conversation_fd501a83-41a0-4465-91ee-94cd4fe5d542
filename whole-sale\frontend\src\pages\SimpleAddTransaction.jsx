import React, { useState, useContext, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import AuthContext from '../context/AuthContext';
import axios from 'axios';
import '../styles/minimal.css';

const SimpleAddTransaction = () => {
  const { user, logout } = useContext(AuthContext);
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [formErrors, setFormErrors] = useState({});
  const [inventoryItems, setInventoryItems] = useState([]);

  const [formData, setFormData] = useState({
    inventoryId: '',
    type: 'in',
    quantity: '',
    reason: ''
  });

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  // Fetch inventory items on component mount
  useEffect(() => {
    const fetchInventoryItems = async () => {
      try {
        const token = localStorage.getItem('token');
        const response = await axios.get('http://localhost:5000/api/inventory', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        setInventoryItems(response.data);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching inventory items:', error);
        setLoading(false);
      }
    };

    fetchInventoryItems();
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.inventoryId) {
      errors.inventoryId = 'Please select an inventory item';
    }

    if (!formData.quantity || formData.quantity <= 0) {
      errors.quantity = 'Please enter a valid quantity';
    }

    if (!formData.reason.trim()) {
      errors.reason = 'Please provide a reason for this transaction';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (validateForm()) {
      setIsSubmitting(true);

      try {
        const token = localStorage.getItem('token');
        const transactionData = {
          type: formData.type,
          quantity: parseInt(formData.quantity),
          reason: formData.reason
        };

        await axios.post(`http://localhost:5000/api/inventory/${formData.inventoryId}/transaction`, transactionData, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        setSuccessMessage('Transaction added successfully!');

        // Reset form after success
        setFormData({
          inventoryId: '',
          type: 'in',
          quantity: '',
          reason: ''
        });

        // Redirect to inventory page after 2 seconds
        setTimeout(() => {
          navigate('/inventory');
        }, 2000);
      } catch (error) {
        console.error('Error adding transaction:', error);
        if (error.response && error.response.data && error.response.data.msg) {
          setFormErrors({ submit: error.response.data.msg });
        } else {
          setFormErrors({ submit: 'Failed to add transaction. Please try again.' });
        }
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  if (loading) {
    return (
      <div className="fade-in">
        <div className="container">
          <div className="loading-spinner">Loading...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="fade-in">
      <header className="header">
        <div className="container header-container">
          <Link to="/" className="brand">WholesaleFlow</Link>

          <nav className="nav">
            <Link to="/dashboard" className="nav-link">Dashboard</Link>
            <Link to="/products" className="nav-link">Products</Link>
            <Link to="/customers" className="nav-link">Customers</Link>
            <Link to="/inventory" className="nav-link active">Inventory</Link>
            {user && user.role === 'admin' && (
              <Link to="/staff" className="nav-link">Staff</Link>
            )}
            <Link to="/login" onClick={handleLogout} className="nav-link">Logout</Link>
          </nav>
        </div>
      </header>

      <div className="container">
        <div className="page-header">
          <h1 className="page-title">Add Transaction</h1>
          <Link to="/inventory" className="btn btn-secondary">
            ← Back to Inventory
          </Link>
        </div>

        {successMessage && (
          <div className="alert alert-success">
            {successMessage}
          </div>
        )}

        {formErrors.submit && (
          <div className="alert alert-error">
            {formErrors.submit}
          </div>
        )}

        <div className="card">
          <div className="card-body">
            <form onSubmit={handleSubmit}>
              <div className="form-group">
                <label htmlFor="inventoryId" className="form-label">
                  Select Inventory Item *
                </label>
                <select
                  id="inventoryId"
                  name="inventoryId"
                  className={`form-control ${formErrors.inventoryId ? 'error' : ''}`}
                  value={formData.inventoryId}
                  onChange={handleChange}
                  required
                >
                  <option value="">Choose an inventory item...</option>
                  {inventoryItems.map(item => (
                    <option key={item._id} value={item._id}>
                      {item.product?.name || 'Unknown Product'} - Current Stock: {item.quantity}
                    </option>
                  ))}
                </select>
                {formErrors.inventoryId && (
                  <div className="error-message">{formErrors.inventoryId}</div>
                )}
              </div>

              <div className="form-group">
                <label htmlFor="type" className="form-label">
                  Transaction Type *
                </label>
                <select
                  id="type"
                  name="type"
                  className="form-control"
                  value={formData.type}
                  onChange={handleChange}
                  required
                >
                  <option value="in">Stock In</option>
                  <option value="out">Stock Out</option>
                  <option value="adjustment">Adjustment</option>
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="quantity" className="form-label">
                  Quantity *
                </label>
                <input
                  type="number"
                  id="quantity"
                  name="quantity"
                  className={`form-control ${formErrors.quantity ? 'error' : ''}`}
                  value={formData.quantity}
                  onChange={handleChange}
                  placeholder="Enter quantity"
                  min="1"
                  required
                />
                {formErrors.quantity && (
                  <div className="error-message">{formErrors.quantity}</div>
                )}
              </div>

              <div className="form-group">
                <label htmlFor="reason" className="form-label">
                  Reason *
                </label>
                <textarea
                  id="reason"
                  name="reason"
                  className={`form-control ${formErrors.reason ? 'error' : ''}`}
                  value={formData.reason}
                  onChange={handleChange}
                  placeholder="Enter reason for this transaction"
                  rows="3"
                  required
                />
                {formErrors.reason && (
                  <div className="error-message">{formErrors.reason}</div>
                )}
              </div>

              <div className="form-actions">
                <Link to="/inventory" className="btn btn-secondary">
                  Cancel
                </Link>
                <button
                  type="submit"
                  className="btn btn-primary"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Adding Transaction...' : 'Add Transaction'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleAddTransaction;
